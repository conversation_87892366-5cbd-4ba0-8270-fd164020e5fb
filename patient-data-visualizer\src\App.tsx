import { useState, useEffect } from 'react'
import { User, Calendar, FileText, Activity, ChevronDown, ChevronRight, Clock, MapPin, Phone, Mail } from 'lucide-react'
import './App.css'

interface PatientData {
  ExtractedAt: string;
  User: any;
  Appointments: any[];
}

function App() {
  const [patientData, setPatientData] = useState<PatientData | null>(null)
  const [selectedSection, setSelectedSection] = useState<string>('overview')
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  useEffect(() => {
    fetch('/Bush_George.json')
      .then(response => response.json())
      .then(data => setPatientData(data))
      .catch(error => console.error('Error loading patient data:', error))
  }, [])

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(id)) {
      newExpanded.delete(id)
    } else {
      newExpanded.add(id)
    }
    setExpandedItems(newExpanded)
  }

  if (!patientData) {
    return (
      <div className="loading">
        <Activity className="animate-spin" size={32} />
        <p>Loading patient data...</p>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    if (!dateString || dateString === 'NaT') return 'N/A'
    return new Date(dateString).toLocaleString()
  }

  const formatValue = (value: any) => {
    if (value === null || value === undefined || value === 'NaT') return 'N/A'
    if (typeof value === 'boolean') return value ? 'Yes' : 'No'
    if (typeof value === 'string' && value.trim() === '') return 'N/A'
    return String(value)
  }

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <User size={32} />
          <div>
            <h1>{patientData.User.FirstName} {patientData.User.LastName}</h1>
            <p>Patient ID: {patientData.User.UserID} | MRN: {patientData.User.MRN}</p>
          </div>
        </div>
        <div className="extraction-info">
          <Clock size={16} />
          <span>Data extracted: {formatDate(patientData.ExtractedAt)}</span>
        </div>
      </header>

      <nav className="navigation">
        <button
          className={selectedSection === 'overview' ? 'active' : ''}
          onClick={() => setSelectedSection('overview')}
        >
          <User size={16} />
          Patient Overview
        </button>
        <button
          className={selectedSection === 'appointments' ? 'active' : ''}
          onClick={() => setSelectedSection('appointments')}
        >
          <Calendar size={16} />
          Appointments ({patientData.Appointments.length})
        </button>
        <button
          className={selectedSection === 'documents' ? 'active' : ''}
          onClick={() => setSelectedSection('documents')}
        >
          <FileText size={16} />
          Documents
        </button>
        <button
          className={selectedSection === 'workflows' ? 'active' : ''}
          onClick={() => setSelectedSection('workflows')}
        >
          <Activity size={16} />
          Workflows
        </button>
      </nav>

      <main className="main-content">
        {selectedSection === 'overview' && (
          <div className="section">
            <h2>Patient Information</h2>
            <div className="info-grid">
              <div className="info-card">
                <h3>Personal Details</h3>
                <div className="info-item">
                  <strong>Full Name:</strong> {patientData.User.FirstName} {patientData.User.MiddleName || ''} {patientData.User.LastName}
                </div>
                <div className="info-item">
                  <strong>Sex:</strong> {formatValue(patientData.User.Sex)}
                </div>
                <div className="info-item">
                  <strong>Date of Birth:</strong> {formatDate(patientData.User.DOB)}
                </div>
                <div className="info-item">
                  <strong>SSN:</strong> {formatValue(patientData.User.SSN)}
                </div>
                <div className="info-item">
                  <strong>Other ID:</strong> {formatValue(patientData.User.OtherId)}
                </div>
              </div>

              <div className="info-card">
                <h3>Contact Information</h3>
                <div className="info-item">
                  <Mail size={16} />
                  <strong>Email:</strong> {formatValue(patientData.User.EmailId)}
                </div>
                <div className="info-item">
                  <MapPin size={16} />
                  <strong>Address:</strong>
                  <div className="address">
                    {formatValue(patientData.User.Address1)}<br/>
                    {patientData.User.Address2 && <>{formatValue(patientData.User.Address2)}<br/></>}
                    {formatValue(patientData.User.City)}, {formatValue(patientData.User.State)} {formatValue(patientData.User.Zip)}<br/>
                    {formatValue(patientData.User.Country)}
                  </div>
                </div>
              </div>

              <div className="info-card">
                <h3>Account Status</h3>
                <div className="info-item">
                  <strong>Active:</strong> {formatValue(patientData.User.Active)}
                </div>
                <div className="info-item">
                  <strong>Has Image:</strong> {formatValue(patientData.User.HasImage)}
                </div>
                <div className="info-item">
                  <strong>View Reports:</strong> {formatValue(patientData.User.ViewReports)}
                </div>
                <div className="info-item">
                  <strong>Organization ID:</strong> {formatValue(patientData.User.OrganizationId)}
                </div>
                <div className="info-item">
                  <strong>Location ID:</strong> {formatValue(patientData.User.LocationID)}
                </div>
              </div>

              <div className="info-card">
                <h3>System Information</h3>
                <div className="info-item">
                  <strong>Source:</strong> {formatValue(patientData.User.Source)}
                </div>
                <div className="info-item">
                  <strong>Created:</strong> {formatDate(patientData.User.CreatedDTTM)}
                </div>
                <div className="info-item">
                  <strong>Last Modified:</strong> {formatDate(patientData.User.ModifiedDTTM)}
                </div>
                <div className="info-item">
                  <strong>Image Date:</strong> {formatDate(patientData.User.UserImageDateTaken)}
                </div>
              </div>
            </div>
          </div>
        )}

        {selectedSection === 'appointments' && (
          <div className="section">
            <h2>Appointments History</h2>
            <div className="appointments-list">
              {patientData.Appointments.map((appointment, index) => (
                <div key={appointment.Appointment.AppointmentId} className="appointment-card">
                  <div
                    className="appointment-header"
                    onClick={() => toggleExpanded(`appointment-${index}`)}
                  >
                    <div className="appointment-summary">
                      <div className="appointment-title">
                        <Calendar size={20} />
                        <span>Appointment #{appointment.Appointment.AppNo}</span>
                        <span className={`status ${appointment.Appointment.Status.trim().toLowerCase().replace(/\s+/g, '-')}`}>
                          {appointment.Appointment.Status.trim()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <span>{formatDate(appointment.Appointment.AppDate)}</span>
                        <span>•</span>
                        <span>{appointment.Appointment.Department}</span>
                        <span>•</span>
                        <span>{appointment.Appointment.Reasonforvisit}</span>
                      </div>
                    </div>
                    {expandedItems.has(`appointment-${index}`) ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
                  </div>

                  {expandedItems.has(`appointment-${index}`) && (
                    <div className="appointment-details-expanded">
                      <div className="details-grid">
                        <div className="detail-section">
                          <h4>Appointment Details</h4>
                          <div className="detail-item"><strong>ID:</strong> {appointment.Appointment.AppointmentId}</div>
                          <div className="detail-item"><strong>Date:</strong> {formatDate(appointment.Appointment.AppDate)}</div>
                          <div className="detail-item"><strong>Time:</strong> {formatDate(appointment.Appointment.AppTime)}</div>
                          <div className="detail-item"><strong>Arrival:</strong> {formatDate(appointment.Appointment.ArrTime)}</div>
                          <div className="detail-item"><strong>Provider ID:</strong> {formatValue(appointment.Appointment.ProviderId)}</div>
                          <div className="detail-item"><strong>Procedure Code:</strong> {formatValue(appointment.Appointment.ProcedureCode)}</div>
                          <div className="detail-item"><strong>Department Code:</strong> {formatValue(appointment.Appointment.ExtDepartmentCode)}</div>
                        </div>

                        <div className="detail-section">
                          <h4>Status & Progress</h4>
                          <div className="detail-item"><strong>Status:</strong> {appointment.Appointment.Status.trim()}</div>
                          <div className="detail-item"><strong>Payment Done:</strong> {formatValue(appointment.Appointment.IsPaymentDone)}</div>
                          <div className="detail-item"><strong>Final Summary:</strong> {formatValue(appointment.Appointment.ReachedFinalSummary)}</div>
                          <div className="detail-item"><strong>Scheduled:</strong> {formatValue(appointment.Appointment.IsScheduled)}</div>
                          <div className="detail-item"><strong>Complete Time:</strong> {formatDate(appointment.Appointment.CompleteTime)}</div>
                          <div className="detail-item"><strong>Session End:</strong> {formatDate(appointment.Appointment.KioskSessionEndTime)}</div>
                        </div>
                      </div>

                      {appointment.Appointments_Support && appointment.Appointments_Support.length > 0 && (
                        <div className="support-info">
                          <h4>Support Information</h4>
                          {appointment.Appointments_Support.map((support: any, supportIndex: number) => (
                            <div key={supportIndex} className="support-details">
                              <div className="detail-item"><strong>Workflow Completed:</strong> {formatValue(support.WorkflowCompleted)}</div>
                              <div className="detail-item"><strong>Screening %:</strong> {formatValue(support.ScreeningPercentage)}%</div>
                              <div className="detail-item"><strong>Source:</strong> {formatValue(support.Source)}</div>
                              <div className="detail-item"><strong>Checked In From:</strong> {formatValue(support.CheckedInFrom)}</div>
                              <div className="detail-item"><strong>Last Screen:</strong> {formatValue(support.LastAnsweredScreenNumber)}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {selectedSection === 'documents' && (
          <div className="section">
            <h2>Documents</h2>
            <div className="documents-list">
              {patientData.Appointments.map((appointment, appointmentIndex) =>
                appointment.OutBound_Document && appointment.OutBound_Document.length > 0 && (
                  <div key={appointmentIndex} className="document-group">
                    <h3>Appointment #{appointment.Appointment.AppNo} Documents</h3>
                    {appointment.OutBound_Document.map((doc: any, docIndex: number) => (
                      <div key={docIndex} className="document-card">
                        <div className="document-header">
                          <FileText size={20} />
                          <div>
                            <h4>Document ID: {doc.DocumentID}</h4>
                            <p>Template ID: {doc.DocumentTemplateID} | Workflow: {doc.WorkFlowType}</p>
                          </div>
                          <span className={`status ${doc.IsProcessed.trim().toLowerCase()}`}>
                            {doc.IsProcessed.trim() === 'N' ? 'Not Processed' :
                             doc.IsProcessed.trim() === 'E' ? 'Error' : 'Processed'}
                          </span>
                        </div>
                        <div className="document-details">
                          <div className="detail-item"><strong>Transaction:</strong> {doc.TransactionNo}</div>
                          <div className="detail-item"><strong>Workflow ID:</strong> {doc.WorkflowID}</div>
                          <div className="detail-item"><strong>Created:</strong> {formatDate(doc.CreatedDTTM)}</div>
                          <div className="detail-item"><strong>Modified:</strong> {formatDate(doc.ModifiedDTTM)}</div>
                          {doc.ProcessMessage && (
                            <div className="detail-item"><strong>Message:</strong> {doc.ProcessMessage}</div>
                          )}
                          {doc.OutboundAPITranID && (
                            <div className="detail-item"><strong>API Transaction ID:</strong> {doc.OutboundAPITranID}</div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )
              )}
            </div>
          </div>
        )}

        {selectedSection === 'workflows' && (
          <div className="section">
            <h2>Workflows</h2>
            <div className="workflows-list">
              {patientData.Appointments.map((appointment, appointmentIndex) =>
                appointment.Workflows && appointment.Workflows.length > 0 && (
                  <div key={appointmentIndex} className="workflow-group">
                    <h3>Appointment #{appointment.Appointment.AppNo} Workflows</h3>
                    {appointment.Workflows.map((workflow: any, workflowIndex: number) => (
                      <div key={workflowIndex} className="workflow-card">
                        {workflow.WorkflowAuditLog && workflow.WorkflowAuditLog.length > 0 && (
                          <div className="audit-log">
                            <h4>Workflow Audit Log</h4>
                            {workflow.WorkflowAuditLog.map((log: any, logIndex: number) => (
                              <div key={logIndex} className="log-entry">
                                <div className="log-header">
                                  <Activity size={16} />
                                  <span className="workflow-type">{log.WorkflowType}</span>
                                  <span className={`status ${log.Status.toLowerCase().replace(/\s+/g, '-')}`}>
                                    {log.Status || 'Unknown'}
                                  </span>
                                </div>
                                <div className="log-details">
                                  <div className="detail-item"><strong>Start:</strong> {formatDate(log.StartDate)}</div>
                                  <div className="detail-item"><strong>End:</strong> {formatDate(log.EndDate)}</div>
                                  <div className="detail-item"><strong>User ID:</strong> {formatValue(log.UserId)}</div>
                                  <div className="detail-item"><strong>Last Screen:</strong> {formatValue(log.LastAnsweredScreenNo)}</div>
                                  <div className="detail-item"><strong>IP Address:</strong> {formatValue(log.IPAddress)}</div>
                                  <div className="detail-item"><strong>User Agent:</strong> {formatValue(log.UserAgent)}</div>
                                  {log.ScreensAccessed && (
                                    <div className="detail-item"><strong>Screens:</strong> {log.ScreensAccessed}</div>
                                  )}
                                  <div className="detail-item"><strong>Completed:</strong> {formatValue(log.IsWorkFlowCompleted)}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        {workflow.KioskWorkflow && workflow.KioskWorkflow.length > 0 && (
                          <div className="kiosk-workflow">
                            <h4>Kiosk Workflow Configuration</h4>
                            {workflow.KioskWorkflow.map((kiosk: any, kioskIndex: number) => (
                              <div key={kioskIndex} className="kiosk-details">
                                <div className="detail-item"><strong>Workflow ID:</strong> {kiosk.KioskWorkflowId}</div>
                                <div className="detail-item"><strong>Type:</strong> {kiosk.WorkflowType}</div>
                                <div className="detail-item"><strong>Expression:</strong> {kiosk.Expression}</div>
                                <div className="detail-item"><strong>Question Template:</strong> {kiosk.QuestionTemplateId}</div>
                                <div className="detail-item"><strong>Active:</strong> {formatValue(kiosk.IsActive)}</div>
                                <div className="detail-item"><strong>Source:</strong> {kiosk.Source}</div>
                                <div className="detail-item"><strong>Created:</strong> {formatDate(kiosk.CreatedDTTM)}</div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  )
}

export default App
