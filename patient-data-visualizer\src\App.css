* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 1rem;
  color: #666;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-content h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.extraction-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.8;
  font-size: 0.85rem;
}

.navigation {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 0 2rem;
  display: flex;
  gap: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navigation button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
}

.navigation button:hover {
  background-color: #f8f9fa;
  color: #333;
}

.navigation button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background-color: #f8f9fa;
}

.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.section h2 {
  margin-bottom: 2rem;
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.info-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
}

.info-card h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
  border-bottom: 2px solid #f1f3f4;
  padding-bottom: 0.5rem;
}

.info-item {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  line-height: 1.5;
}

.info-item strong {
  min-width: 120px;
  color: #555;
  font-weight: 500;
}

.address {
  margin-left: 0.5rem;
  color: #666;
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.appointment-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
  overflow: hidden;
}

.appointment-header {
  padding: 1.5rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.appointment-header:hover {
  background-color: #f8f9fa;
}

.appointment-summary {
  flex: 1;
}

.appointment-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.appointment-title span:first-of-type {
  font-weight: 600;
  font-size: 1.1rem;
}

.status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status.open {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status.check-in-completed {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status.in-progress {
  background-color: #fff3e0;
  color: #f57c00;
}

.status.cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

.status.n, .status.e {
  background-color: #ffebee;
  color: #d32f2f;
}

.status.completed {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.appointment-details {
  color: #666;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.appointment-details-expanded {
  padding: 0 1.5rem 1.5rem;
  border-top: 1px solid #f1f3f4;
  background-color: #fafbfc;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.detail-section h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.detail-item {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.detail-item strong {
  color: #555;
  min-width: 140px;
  display: inline-block;
}

.support-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.support-info h4 {
  margin-bottom: 1rem;
  color: #333;
}

.support-details {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.documents-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.document-group h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.document-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
  margin-bottom: 1rem;
}

.document-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.document-header > div {
  flex: 1;
}

.document-header h4 {
  margin: 0 0 0.25rem 0;
  color: #333;
  font-size: 1.1rem;
}

.document-header p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.document-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.75rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #f1f3f4;
}

.workflows-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.workflow-group h3 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.workflow-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
  margin-bottom: 1rem;
}

.audit-log {
  margin-bottom: 2rem;
}

.audit-log h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.log-entry {
  background: #fafbfc;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.log-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.workflow-type {
  font-weight: 600;
  color: #333;
}

.log-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.5rem;
}

.kiosk-workflow {
  padding-top: 1rem;
  border-top: 1px solid #e1e5e9;
}

.kiosk-workflow h4 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.kiosk-details {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .navigation {
    flex-wrap: wrap;
    padding: 0 1rem;
  }

  .navigation button {
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
  }

  .main-content {
    padding: 1rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .document-details {
    grid-template-columns: 1fr;
  }

  .log-details {
    grid-template-columns: 1fr;
  }

  .kiosk-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }

  .appointment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .appointment-title {
    flex-wrap: wrap;
  }

  .document-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
